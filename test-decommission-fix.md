# BBB Manager Decommissioning Fix - Test Plan

## 🔍 **Issues Fixed**

### **Issue 1: DNS Cleanup Failure** 🌐
- **Problem**: DNS records were not being deleted during spindown
- **Root Cause**: Error handling was not robust enough, and 404 errors were treated as failures
- **Fix**: Added better error handling for 404 responses (record already deleted)

### **Issue 2: Scalelite Deregistration Failure** ⚖️
- **Problem**: Servers remained attached to Scalelite after decommissioning
- **Root Cause**: Complex Jinja2 server ID extraction was failing
- **Fix**: Added fallback shell-based extraction method and improved error handling

### **Issue 3: Error Handling Flow** ❌
- **Problem**: When Ansible decommissioning failed, manual cleanup was insufficient
- **Root Cause**: Manual cleanup methods were not robust enough
- **Fix**: Enhanced manual cleanup with proper Scalelite server removal

## 🛠️ **Changes Made**

### **1. Enhanced BBB Scheduler Service** (`nest-app/src/bbb/bbb-scheduler.service.ts`)
- **Improved spinDown method**: Better error handling and cleanup order
- **Added helper methods**:
  - `performManualCleanup()`: Robust manual Scalelite cleanup
  - `performDnsCleanup()`: Enhanced DNS cleanup with error handling
  - `performDropletDeletion()`: Improved droplet deletion
- **Enhanced Scalelite cleanup**: Actual server removal instead of just logging

### **2. Improved Scalelite Role** (`bbb-ansible/roles/scalelite/tasks/main.yml`)
- **Added fallback server ID extraction**: Shell-based method when Jinja2 fails
- **Enhanced debugging**: Better logging of extraction process
- **Improved error handling**: More robust server ID detection

### **3. Enhanced Scalelite Unregistration** (`bbb-ansible/roles/scalelite/tasks/unregister_server.yml`)
- **Better error handling**: Don't fail on "server not found" errors
- **Enhanced logging**: Detailed result reporting
- **Improved status messages**: Clear success/failure indication

### **4. Improved DNS Service** (`nest-app/src/dns/dns.service.ts`)
- **Better 404 handling**: Treat "record not found" as success
- **Enhanced error logging**: More detailed error information
- **Improved retry logic**: Better handling of edge cases

## 🧪 **Testing Steps**

### **1. Test DNS Cleanup**
```bash
# Check current DNS records
curl -X GET "https://api.digitalocean.com/v2/domains/geerd.net/records" \
  -H "Authorization: Bearer YOUR_TOKEN" | jq '.domain_records[] | select(.name | contains("bbb-"))'

# Trigger manual server spindown
curl -X POST http://localhost:3000/bbb-scheduler/trigger

# Verify DNS records are cleaned up
```

### **2. Test Scalelite Cleanup**
```bash
# Check current Scalelite servers
ssh root@************** "docker exec scalelite-api bundle exec rake servers"

# Trigger decommissioning
curl -X POST http://localhost:3000/bbb-scheduler/trigger

# Verify servers are removed from Scalelite
ssh root@************** "docker exec scalelite-api bundle exec rake servers"
```

### **3. Test Complete Flow**
```bash
# Monitor logs during decommissioning
docker logs -f bbb-manager-app

# Check system status
curl http://localhost:3000/bbb-scheduler/status
curl http://localhost:3000/cache-debug/servers
```

## 🎯 **Expected Results**

### **Before Fix**
- ❌ DNS records remain after server deletion
- ❌ Servers stay registered in Scalelite
- ❌ Manual cleanup fails silently

### **After Fix**
- ✅ DNS records are properly deleted
- ✅ Servers are disabled and removed from Scalelite
- ✅ Manual cleanup works when Ansible fails
- ✅ Better error reporting and logging

## 🔧 **Key Improvements**

1. **Robust Error Handling**: 404 errors are now treated appropriately
2. **Fallback Mechanisms**: Multiple methods for server ID extraction
3. **Better Logging**: Detailed status reporting throughout the process
4. **Manual Cleanup**: Functional fallback when Ansible fails
5. **Graceful Degradation**: System continues even if some cleanup steps fail

## 📊 **Monitoring**

Watch these logs to verify fixes:
- `🌐 Cleaning up DNS records for [server-name]`
- `✅ DNS record deleted successfully`
- `⚖️ Manually removing server from Scalelite`
- `✅ Server [id] removed from Scalelite successfully`
- `✅ Server [id] spindown completed successfully`
